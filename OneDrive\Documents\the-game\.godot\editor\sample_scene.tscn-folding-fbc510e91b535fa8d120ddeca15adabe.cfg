[folding]

node_unfolds=[NodePath("DirectionalLight3D"), PackedStringArray("Light"), NodePath("RigidBody3D/CollisionShape3D"), PackedStringArray("Transform", "shape"), NodePath("RigidBody3D/MeshInstance3D"), PackedStringArray("mesh")]
resource_unfolds=["res://sample_scene.tscn::BoxShape3D_flkeq", PackedStringArray(), "res://sample_scene.tscn::BoxMesh_26al8", PackedStringArray()]
nodes_folded=[NodePath("RigidBody3D")]
