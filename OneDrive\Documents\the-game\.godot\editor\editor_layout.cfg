[docks]

dock_3_selected_tab_idx=0
dock_4_selected_tab_idx=0
dock_5_selected_tab_idx=0
dock_floating={}
dock_bottom=[]
dock_closed=[]
dock_split_2=0
dock_split_3=0
dock_hsplit_1=0
dock_hsplit_2=270
dock_hsplit_3=-270
dock_hsplit_4=0
dock_filesystem_h_split_offset=240
dock_filesystem_v_split_offset=0
dock_filesystem_display_mode=0
dock_filesystem_file_sort=0
dock_filesystem_file_list_display_mode=1
dock_filesystem_selected_paths=PackedStringArray("res://FPSController/FPSController.tscn")
dock_filesystem_uncollapsed_paths=PackedStringArray("Favorites", "res://", "res://FPSController/")
dock_3="Scene,Import"
dock_4="FileSystem"
dock_5="Inspector,Node,History"

[EditorNode]

open_scenes=PackedStringArray("res://sample_scene.tscn", "res://FPSController/FPSController.tscn", "res://a_single_block.tscn")
current_scene="res://sample_scene.tscn"
center_split_offset=0
selected_default_debugger_tab_idx=0
selected_main_editor_idx=1
selected_bottom_panel_item=0

[ScriptEditor]

open_scripts=["res://FPSController/fps_controller.gd"]
selected_script="res://FPSController/fps_controller.gd"
open_help=[]
script_split_offset=70
list_split_offset=0
zoom_factor=1.0

[ShaderEditor]

open_shaders=[]
split_offset=0
selected_shader=""
text_shader_zoom_factor=1.0
