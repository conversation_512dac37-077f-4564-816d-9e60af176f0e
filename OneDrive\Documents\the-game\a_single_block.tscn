[gd_scene load_steps=3 format=3 uid="uid://biwt1qj5qwp0d"]

[sub_resource type="BoxShape3D" id="BoxShape3D_c8ovu"]

[sub_resource type="BoxMesh" id="BoxMesh_qy6tc"]

[node name="a single block" type="Node3D"]

[node name="a block" type="RigidBody3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.011, 0, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="a block"]
shape = SubResource("BoxShape3D_c8ovu")

[node name="MeshInstance3D" type="MeshInstance3D" parent="a block"]
mesh = SubResource("BoxMesh_qy6tc")
