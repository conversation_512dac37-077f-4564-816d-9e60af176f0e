[folding]

node_unfolds=[Node<PERSON>ath("CollisionShape3D"), PackedStringArray("Transform"), NodePath("WorldModel/MeshInstance3D"), PackedStringArray("Transform"), NodePath("WorldModel/MeshInstance3D2"), PackedStringArray("mesh"), NodePath("Head"), PackedStringArray("Transform"), NodePath("Head/Camera3D"), PackedStringArray("Transform")]
resource_unfolds=["res://FPSController/FPSController.tscn::CapsuleShape3D_rvewv", PackedStringArray(), "res://FPSController/FPSController.tscn::CapsuleMesh_s1d2x", PackedStringArray(), "res://FPSController/FPSController.tscn::BoxMesh_6gq3q", PackedStringArray()]
nodes_folded=[]
