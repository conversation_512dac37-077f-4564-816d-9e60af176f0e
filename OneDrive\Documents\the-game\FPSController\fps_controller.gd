extends CharacterBody3D

@export var look_sensitivity : float = 0.006
@export var jump_vel := 6.8
@export var auto_bhop := true
@export var walk_speed := 7.0
@export var sprint_speed := 8.5

const HEADBOB_MOVE_AMOUNT = 0.06
const HEADBOB_FREQ = 2.4
var headbob_time := 0.0

var wish_dir := Vector3.ZERO

@export var air_cap := 0.85
@export var air_accel :=  800.0
@export var air_move_speed := 500.0
# ANYTHING BELOW IS WHERE THE SHIT HITS THE FAN


func get_move_speed() -> float:
	return sprint_speed if Input.is_action_just_pressed("sprint") else walk_speed

# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	for child in %WorldModel.find_children("*", "VisualInstance3D"):
		child.set_layer_mask_value(1, false)
		child.set_layer_mask_value(2, true)

func _unhandled_input(event: InputEvent) -> void:
	if event is InputEventMouseButton:
		Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)
	elif event.is_action_pressed("ui_cancel"):
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
	
	if Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:
		if event is InputEventMouseMotion:
			rotate_y(-event.relative.x * look_sensitivity)
			%Camera3D.rotate_x(-event.relative.y * look_sensitivity)
			%Camera3D.rotation.x = clamp($"%Camera3D".rotation.x, deg_to_rad(-90), deg_to_rad(90))

func _headbob_efect(delta):
	headbob_time += delta * self.velocity.length()
	%Camera3D.transform.origin = Vector3(
		cos(headbob_time * HEADBOB_FREQ * 0.5) * HEADBOB_MOVE_AMOUNT,
		sin(headbob_time * HEADBOB_FREQ) * HEADBOB_MOVE_AMOUNT	,
		0
	)

# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	pass

func _handle_air_physics(delta) -> void:
	self.velocity.y -= ProjectSettings.get_setting("physics/3d/default_gravity") * delta
	
	var cur_speed_in_wish_dir = self.velocity.dot(wish_dir)
	var max_speed = min((air_move_speed * wish_dir).length(), air_cap)
	var add_speed_till_max = max_speed - cur_speed_in_wish_dir 
	
	if add_speed_till_max > 0:
		var accel_speed = air_accel * air_move_speed * delta
		accel_speed = min(accel_speed, add_speed_till_max)
		self.velocity += accel_speed * wish_dir

func _handle_ground_physics(delta) -> void:
	self.velocity.x = wish_dir.x * get_move_speed()
	self.velocity.z = wish_dir.z * get_move_speed()
	_headbob_efect(delta)

func _physics_process(delta: float) -> void:
	var input_dir = Input.get_vector("a", "d", "w", "s").normalized()
	wish_dir = self.global_transform.basis * Vector3(input_dir.x, 0., 	input_dir.y)
	
	if is_on_floor():
		if Input.is_action_just_pressed("jump") or (auto_bhop and Input.is_action_just_pressed("jump")):
			self.velocity.y = jump_vel
		_handle_ground_physics(delta)
	else :
		_handle_air_physics(delta)
	
	move_and_slide()
