[gd_scene load_steps=5 format=3 uid="uid://c021pfjojha3x"]

[ext_resource type="PackedScene" uid="uid://dgec7ksv1daw5" path="res://FPSController/FPSController.tscn" id="1_qlo2s"]
[ext_resource type="PackedScene" uid="uid://biwt1qj5qwp0d" path="res://a_single_block.tscn" id="2_j82uq"]

[sub_resource type="BoxShape3D" id="BoxShape3D_flkeq"]
size = Vector3(50, 1, 50)

[sub_resource type="BoxMesh" id="BoxMesh_26al8"]
size = Vector3(50, 1, 50)

[node name="Node3D" type="Node3D"]

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.522498, -0.58041, 0.624596, 0, 0.732543, 0.680721, -0.85264, -0.355676, 0.382753, 0, 0, 0)
light_color = Color(0.734091, 0.692933, 0.689058, 1)
shadow_enabled = true

[node name="RigidBody3D" type="RigidBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="RigidBody3D"]
shape = SubResource("BoxShape3D_flkeq")

[node name="MeshInstance3D" type="MeshInstance3D" parent="RigidBody3D"]
mesh = SubResource("BoxMesh_26al8")

[node name="CharacterBody3D" parent="." instance=ExtResource("1_qlo2s")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.272158, 1.282, 0.450331)

[node name="a single block" parent="." instance=ExtResource("2_j82uq")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4.93484, 2.18366, -0.589343)
