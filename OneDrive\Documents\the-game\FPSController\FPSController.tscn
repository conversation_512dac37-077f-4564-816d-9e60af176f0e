[gd_scene load_steps=5 format=3 uid="uid://dgec7ksv1daw5"]

[ext_resource type="Script" path="res://FPSController/fps_controller.gd" id="1_juye6"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_rvewv"]

[sub_resource type="CapsuleMesh" id="CapsuleMesh_s1d2x"]

[sub_resource type="BoxMesh" id="BoxMesh_6gq3q"]
size = Vector3(0.2, 0.2, 1)

[node name="CharacterBody3D" type="CharacterBody3D"]
script = ExtResource("1_juye6")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)
shape = SubResource("CapsuleShape3D_rvewv")

[node name="WorldModel" type="Node3D" parent="."]
unique_name_in_owner = true

[node name="MeshInstance3D" type="MeshInstance3D" parent="WorldModel"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)
mesh = SubResource("CapsuleMesh_s1d2x")

[node name="MeshInstance3D2" type="MeshInstance3D" parent="WorldModel"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.285262, 1.36487, -0.774149)
mesh = SubResource("BoxMesh_6gq3q")

[node name="Head" type="Node3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.79, 0)

[node name="Camera3D" type="Camera3D" parent="Head"]
unique_name_in_owner = true
cull_mask = 1048573
